// lib/activation-codes.js
import { createClient } from 'redis'
import { v4 as uuidv4 } from 'uuid'

// Redis存储键名常量
const CODES_KEY = 'activation-codes'
const CODES_INDEX_KEY = 'activation-codes-index' // 用于快速查找

// 创建Redis客户端
let redis = null

async function getRedisClient() {
  if (!redis) {
    redis = createClient({
      url: process.env.REDIS_URL
    })

    redis.on('error', (err) => {
      console.error('Redis Client Error:', err)
    })

    await redis.connect()
  }
  return redis
}

// 读取激活码数据
async function readCodesData() {
  try {
    const client = await getRedisClient()
    const codesStr = await client.get(CODES_KEY)
    return codesStr ? JSON.parse(codesStr) : []
  } catch (error) {
    console.error('Error reading activation codes from Redis:', error)
    return []
  }
}

// 写入激活码数据
async function writeCodesData(codes) {
  try {
    const client = await getRedisClient()
    // 同时更新主数据和索引
    await Promise.all([
      client.set(CODES_KEY, JSON.stringify(codes)),
      updateCodesIndex(codes)
    ])
    return true
  } catch (error) {
    console.error('Error writing activation codes to Redis:', error)
    return false
  }
}

// 更新激活码索引（用于快速查找）
async function updateCodesIndex(codes) {
  try {
    const client = await getRedisClient()
    const index = {}
    codes.forEach(code => {
      index[code.code] = code.id
      index[code.id] = code.code
    })
    await client.set(CODES_INDEX_KEY, JSON.stringify(index))
  } catch (error) {
    console.error('Error updating codes index:', error)
  }
}

// 生成激活码
export async function generateActivationCode(description = '', expiresInDays = 30, maxUsageCount = 1) {
  const codes = await readCodesData()
  const code = uuidv4().replace(/-/g, '').substring(0, 16).toUpperCase()
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + expiresInDays)

  const newCode = {
    id: uuidv4(),
    code,
    description,
    createdAt: new Date().toISOString(),
    expiresAt: expiresAt.toISOString(),
    maxUsageCount: Math.max(1, parseInt(maxUsageCount) || 1), // 确保至少为1
    usedCount: 0,
    isUsed: false, // 保持向后兼容，当 usedCount >= maxUsageCount 时为 true
    usedAt: null,
    usedBy: null, // 对于多次使用的激活码，这将存储最后一次使用者
    usedByList: [], // 新增：存储所有使用者的列表
    isActive: true
  }

  codes.push(newCode)

  if (await writeCodesData(codes)) {
    return newCode
  }
  return null
}

// 验证激活码
export async function validateActivationCode(code) {
  const codes = await readCodesData()
  const activationCode = codes.find(c => c.code === code.toUpperCase())

  if (!activationCode) {
    return { valid: false, error: 'INVALID_CODE' }
  }

  if (!activationCode.isActive) {
    return { valid: false, error: 'DISABLED_CODE' }
  }

  // 检查使用次数限制
  const usedCount = activationCode.usedCount || 0
  const maxUsageCount = activationCode.maxUsageCount || 1

  if (usedCount >= maxUsageCount) {
    return { valid: false, error: 'USAGE_LIMIT_EXCEEDED' }
  }

  // 保持向后兼容性检查
  if (activationCode.isUsed && maxUsageCount === 1) {
    return { valid: false, error: 'USED_CODE' }
  }

  if (new Date() > new Date(activationCode.expiresAt)) {
    return { valid: false, error: 'EXPIRED_CODE' }
  }

  return { valid: true, code: activationCode }
}

// 使用激活码
export async function useActivationCode(code, userEmail) {
  const codes = await readCodesData()
  const codeIndex = codes.findIndex(c => c.code === code.toUpperCase())

  if (codeIndex === -1) {
    return false
  }

  const validation = await validateActivationCode(code)
  if (!validation.valid) {
    return false
  }

  const activationCode = codes[codeIndex]

  // 增加使用次数
  activationCode.usedCount = (activationCode.usedCount || 0) + 1

  // 更新使用者列表
  if (!activationCode.usedByList) {
    activationCode.usedByList = []
  }
  activationCode.usedByList.push({
    email: userEmail,
    usedAt: new Date().toISOString()
  })

  // 更新最后使用信息（保持向后兼容）
  activationCode.usedAt = new Date().toISOString()
  activationCode.usedBy = userEmail

  // 如果达到最大使用次数，标记为已使用（保持向后兼容）
  const maxUsageCount = activationCode.maxUsageCount || 1
  if (activationCode.usedCount >= maxUsageCount) {
    activationCode.isUsed = true
  }

  return await writeCodesData(codes)
}

// 获取所有激活码
export async function getAllActivationCodes() {
  return await readCodesData()
}

// 禁用激活码
export async function disableActivationCode(codeId) {
  const codes = await readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)

  if (codeIndex === -1) {
    return false
  }

  codes[codeIndex].isActive = false
  return await writeCodesData(codes)
}

// 启用激活码
export async function enableActivationCode(codeId) {
  const codes = await readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)

  if (codeIndex === -1) {
    return false
  }

  codes[codeIndex].isActive = true
  return await writeCodesData(codes)
}

// 删除激活码
export async function deleteActivationCode(codeId) {
  const codes = await readCodesData()
  const filteredCodes = codes.filter(c => c.id !== codeId)

  if (filteredCodes.length === codes.length) {
    return false // 没有找到要删除的代码
  }

  return await writeCodesData(filteredCodes)
}

// 获取所有使用者列表
export async function getAllUsers() {
  const codes = await readCodesData()
  const users = []

  codes.forEach(code => {
    if (code.usedByList && code.usedByList.length > 0) {
      code.usedByList.forEach(usage => {
        users.push({
          email: usage.email,
          usedAt: usage.usedAt,
          activationCode: code.code,
          codeDescription: code.description || '无描述',
          codeId: code.id,
          maxUsageCount: code.maxUsageCount || 1,
          currentUsedCount: code.usedCount || 0
        })
      })
    }
  })

  // 按使用时间倒序排列
  users.sort((a, b) => new Date(b.usedAt) - new Date(a.usedAt))

  return users
}

// 获取激活码统计信息
export async function getActivationCodeStats() {
  const codes = await readCodesData()
  const total = codes.length
  const active = codes.filter(c => c.isActive).length
  const expired = codes.filter(c => new Date() > new Date(c.expiresAt)).length

  // 重新计算使用状态，考虑使用次数
  const used = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return usedCount >= maxUsageCount
  }).length

  const available = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return c.isActive &&
           usedCount < maxUsageCount &&
           new Date() <= new Date(c.expiresAt)
  }).length

  // 新增统计：多次使用激活码相关
  const multiUse = codes.filter(c => (c.maxUsageCount || 1) > 1).length
  const totalUsages = codes.reduce((sum, c) => sum + (c.usedCount || 0), 0)
  const partiallyUsed = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return usedCount > 0 && usedCount < maxUsageCount
  }).length

  return {
    total,
    active,
    used,
    expired,
    available,
    multiUse,
    totalUsages,
    partiallyUsed
  }
}

// 数据迁移和管理功能

// 从JSON文件导入数据到KV存储
export async function importFromJSON(jsonData) {
  try {
    const codes = Array.isArray(jsonData) ? jsonData : []
    const success = await writeCodesData(codes)
    return { success, count: codes.length }
  } catch (error) {
    console.error('Error importing data to KV:', error)
    return { success: false, error: error.message }
  }
}

// 导出KV数据为JSON格式
export async function exportToJSON() {
  try {
    const codes = await readCodesData()
    return { success: true, data: codes, count: codes.length }
  } catch (error) {
    console.error('Error exporting data from KV:', error)
    return { success: false, error: error.message }
  }
}

// 清空所有激活码数据（谨慎使用）
export async function clearAllData() {
  try {
    const client = await getRedisClient()
    await Promise.all([
      client.del(CODES_KEY),
      client.del(CODES_INDEX_KEY)
    ])
    return true
  } catch (error) {
    console.error('Error clearing Redis data:', error)
    return false
  }
}

// 获取Redis存储状态信息
export async function getStorageInfo() {
  try {
    const codes = await readCodesData()
    const client = await getRedisClient()
    const indexStr = await client.get(CODES_INDEX_KEY)
    const index = indexStr ? JSON.parse(indexStr) : null

    return {
      codesCount: codes.length,
      indexSize: index ? Object.keys(index).length : 0,
      lastUpdated: new Date().toISOString(),
      storageType: 'Vercel Redis'
    }
  } catch (error) {
    console.error('Error getting storage info:', error)
    return {
      codesCount: 0,
      indexSize: 0,
      lastUpdated: null,
      storageType: 'Vercel Redis',
      error: error.message
    }
  }
}

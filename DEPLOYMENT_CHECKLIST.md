# 🚀 Great Heights School 部署检查清单

## ✅ 部署前检查

### 1. 环境变量配置
确保以下环境变量已正确设置：

```env
# 学校邮箱域名
EMAIL_DOMAIN=ghs.edu.kg

# Google Admin SDK (用于创建和管理用户账号)
GOOGLE_CLIENT_ID=your-admin-client-id
GOOGLE_CLIENT_SECRET=your-admin-client-secret
GOOGLE_REFRESH_TOKEN=your-admin-refresh-token

# Google OAuth (用于学生登录和管理员登录)
GOOGLE_OAUTH_CLIENT_ID=your-oauth-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-oauth-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://yourdomain.com/api/auth/google/callback

# 管理员配置 (OAuth认证)
SESSION_SECRET=your-32-char-secret-key

# 应用配置
NEXTAUTH_URL=https://yourdomain.com

# Vercel Redis 存储配置 (部署到Vercel时自动生成)
REDIS_URL=redis://default:<EMAIL>:port
```

### 2. Vercel Redis 数据库配置
确保在Vercel项目中创建Redis数据库：

1. 登录 [Vercel Dashboard](https://vercel.com/dashboard)
2. 进入项目设置 → Storage 标签
3. 创建新的 Redis 数据库
4. 环境变量会自动添加到项目中：
   - `REDIS_URL`

### 3. Google Cloud 配置

#### Google Admin SDK 设置：
- [ ] 创建 Google Cloud 项目
- [ ] 启用 Admin SDK API
- [ ] 创建 OAuth 2.0 客户端 ID（用于管理员操作）
- [ ] 获取 Refresh Token
- [ ] 确保有 Google Workspace 管理员权限

#### Google OAuth 设置：
- [ ] 在同一项目中创建另一个 OAuth 2.0 客户端 ID（用于学生登录）
- [ ] 配置授权重定向 URI：`https://yourdomain.com/api/auth/google/callback`
- [ ] 设置 OAuth 同意屏幕

### 3. 安全配置
- [ ] 更改默认管理员密码（不要使用 `admin123`）
- [ ] 生成安全的 SESSION_SECRET（至少32个字符）
- [ ] 确保所有敏感信息不在代码中硬编码
- [ ] 验证 HTTPS 配置

### 4. 功能测试

#### 注册流程：
- [ ] 访问首页显示正确
- [ ] 注册页面可以正常访问
- [ ] 激活码验证功能正常（测试码：`DEMO2025GHS`）
- [ ] 用户注册成功后能创建 Google Workspace 账号

#### 登录流程：
- [ ] Google OAuth 登录流程正常
- [ ] 邮箱域名验证正确（只允许 @ghs.edu.kg）
- [ ] 登录成功后跳转到学生门户
- [ ] 登出功能正常

#### 学生门户：
- [ ] 学生信息显示正确
- [ ] 学生证页面可访问
- [ ] 邮箱别名管理功能正常
- [ ] 密码重置功能正常
- [ ] 账号删除功能正常

#### 管理员功能：
- [ ] 管理员登录正常（`/admin/login`）
- [ ] 激活码管理面板功能完整
- [ ] 可以生成、查看、启用/禁用、删除激活码
- [ ] 统计信息显示正确

### 5. 性能和兼容性
- [ ] 页面加载速度正常
- [ ] 移动端响应式设计正常
- [ ] 各主流浏览器兼容性测试
- [ ] API 响应时间合理

## 🔧 部署步骤

### Vercel 部署：
1. 连接 GitHub 仓库到 Vercel
2. 在 Vercel 项目设置中添加环境变量
3. 部署并测试所有功能
4. 配置自定义域名（如需要）

### 域名配置：
1. 更新 Google OAuth 重定向 URI 为生产域名
2. 更新 `NEXTAUTH_URL` 环境变量
3. 确保 SSL 证书正确配置

## 🚨 常见问题排查

### Google OAuth 错误：
- 检查客户端 ID 和密钥是否正确
- 确认重定向 URI 配置正确
- 验证 OAuth 同意屏幕设置

### 激活码问题：
- 确保 Vercel Redis 数据库已创建并连接
- 检查 Redis 环境变量配置
- 验证激活码生成和存储逻辑

### 用户创建失败：
- 检查 Google Admin SDK 权限
- 验证 Refresh Token 有效性
- 确认 Google Workspace 配置

### 登录重定向问题：
- 检查 Cookie 设置
- 验证域名配置
- 确认 HTTPS 设置

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. Vercel 部署日志
3. Google Cloud Console 配置
4. 环境变量设置

---

**开发团队：** Garbage Human Studio  
**项目：** Great Heights School 学生注册系统  
**版本：** 1.0.0
